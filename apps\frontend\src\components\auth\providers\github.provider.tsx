import { useCallback } from 'react';
import { useFetch } from '@gitroom/helpers/utils/custom.fetch';
import interClass from '@gitroom/react/helpers/inter.font';

export const GithubProvider = () => {
  const fetch = useFetch();
  const gotoLogin = useCallback(async () => {
    const link = await (await fetch('/auth/oauth/GITHUB')).text();
    window.location.href = link;
  }, []);

  return (
    <div
      onClick={gotoLogin}
      className={`cursor-pointer bg-white h-[44px] rounded-[4px] flex justify-center items-center text-customColor16 ${interClass} gap-[4px]`}
    >
      <div>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="21"
          height="20"
          viewBox="0 0 21 20"
          fill="none"
        >
          <path
            d="M16.7742 5.91251C16.9653 5.29626 17.0265 4.64711 16.9542 4.00599C16.8819 3.36487 16.6775 2.7457 16.3539 2.18751C16.299 2.09248 16.2201 2.01357 16.1251 1.95871C16.03 1.90385 15.9222 1.87499 15.8125 1.87501C15.0845 1.87349 14.3663 2.04225 13.7151 2.36781C13.064 2.69337 12.4981 3.16671 12.0625 3.75001H10.1875C9.75193 3.16671 9.18598 2.69337 8.53485 2.36781C7.88372 2.04225 7.16548 1.87349 6.4375 1.87501C6.32777 1.87499 6.21996 1.90385 6.12492 1.95871C6.02988 2.01357 5.95096 2.09248 5.89609 2.18751C5.57254 2.7457 5.36815 3.36487 5.2958 4.00599C5.22346 4.64711 5.28474 5.29626 5.47578 5.91251C5.08963 6.58651 4.88278 7.34827 4.875 8.12501V8.75001C4.87632 9.80751 5.26021 10.8288 5.9558 11.6254C6.65139 12.4219 7.61169 12.9399 8.65938 13.0836C8.23173 13.6308 7.9996 14.3055 8 15V15.625H6.125C5.62772 15.625 5.15081 15.4275 4.79917 15.0758C4.44754 14.7242 4.25 14.2473 4.25 13.75C4.25 13.3396 4.16917 12.9333 4.01212 12.5541C3.85508 12.175 3.62489 11.8305 3.33471 11.5403C3.04453 11.2501 2.70003 11.0199 2.32089 10.8629C1.94174 10.7058 1.53538 10.625 1.125 10.625C0.95924 10.625 0.800269 10.6909 0.683058 10.8081C0.565848 10.9253 0.5 11.0842 0.5 11.25C0.5 11.4158 0.565848 11.5747 0.683058 11.692C0.800269 11.8092 0.95924 11.875 1.125 11.875C1.62228 11.875 2.09919 12.0726 2.45083 12.4242C2.80246 12.7758 3 13.2527 3 13.75C3 14.5788 3.32924 15.3737 3.91529 15.9597C4.50134 16.5458 5.2962 16.875 6.125 16.875H8V18.125C8 18.2908 8.06585 18.4497 8.18306 18.567C8.30027 18.6842 8.45924 18.75 8.625 18.75C8.79076 18.75 8.94973 18.6842 9.06694 18.567C9.18415 18.4497 9.25 18.2908 9.25 18.125V15C9.25 14.5027 9.44754 14.0258 9.79917 13.6742C10.1508 13.3226 10.6277 13.125 11.125 13.125C11.6223 13.125 12.0992 13.3226 12.4508 13.6742C12.8025 14.0258 13 14.5027 13 15V18.125C13 18.2908 13.0658 18.4497 13.1831 18.567C13.3003 18.6842 13.4592 18.75 13.625 18.75C13.7908 18.75 13.9497 18.6842 14.0669 18.567C14.1842 18.4497 14.25 18.2908 14.25 18.125V15C14.2504 14.3055 14.0183 13.6308 13.5906 13.0836C14.6383 12.9399 15.5986 12.4219 16.2942 11.6254C16.9898 10.8288 17.3737 9.80751 17.375 8.75001V8.12501C17.3672 7.34827 17.1604 6.58651 16.7742 5.91251ZM16.125 8.75001C16.125 9.57881 15.7958 10.3737 15.2097 10.9597C14.6237 11.5458 13.8288 11.875 13 11.875H9.25C8.4212 11.875 7.62634 11.5458 7.04029 10.9597C6.45424 10.3737 6.125 9.57881 6.125 8.75001V8.12501C6.13266 7.50003 6.31978 6.89042 6.66406 6.36876C6.72824 6.28417 6.76981 6.18461 6.78485 6.0795C6.79988 5.97439 6.78789 5.86717 6.75 5.76798C6.5872 5.34813 6.50886 4.90029 6.51945 4.45011C6.53004 3.99993 6.62936 3.55627 6.81172 3.14454C7.32322 3.19957 7.81577 3.36901 8.25287 3.6403C8.68997 3.91159 9.06041 4.27778 9.33672 4.71173C9.39303 4.79978 9.47051 4.8723 9.56209 4.92267C9.65368 4.97303 9.75642 4.99962 9.86094 5.00001H12.3883C12.4932 5.00001 12.5964 4.97361 12.6884 4.92323C12.7805 4.87285 12.8583 4.80011 12.9148 4.71173C13.1911 4.27775 13.5615 3.91153 13.9986 3.64023C14.4358 3.36893 14.9283 3.19951 15.4398 3.14454C15.622 3.55637 15.721 4.00009 15.7313 4.45027C15.7417 4.90044 15.663 5.34823 15.5 5.76798C15.4622 5.86622 15.4496 5.97235 15.4632 6.07672C15.4769 6.18109 15.5164 6.2804 15.5781 6.36563C15.9258 6.8873 16.1157 7.49816 16.125 8.12501V8.75001Z"
            fill="#121A2D"
          />
        </svg>
      </div>
      <div>Sign in with GitHub</div>
    </div>
  );
};
