import { withProvider } from '@gitroom/frontend/components/launches/providers/high.order.provider';
import { FC, useCallback } from 'react';
import { useSettings } from '@gitroom/frontend/components/launches/helpers/use.values';
import { useFieldArray } from 'react-hook-form';
import { deleteDialog } from '@gitroom/react/helpers/delete.dialog';
import { Button } from '@gitroom/react/form/button';
import { Subreddit } from './subreddit';

const WrapcastProvider: FC = () => {
  const { register, control } = useSettings();
  const { fields, append, remove } = useFieldArray({
    control, // control props comes from useForm (optional: if you are using FormContext)
    name: 'subreddit', // unique name for your Field Array
  });

  const addField = useCallback(() => {
    append({});
  }, [fields, append]);

  const deleteField = useCallback(
    (index: number) => async () => {
      if (
        !(await deleteDialog('Are you sure you want to delete this Subreddit?'))
      )
        return;
      remove(index);
    },
    [fields, remove]
  );

  return (
    <>
      <div className="flex flex-col gap-[20px] mb-[20px]">
        {fields.map((field, index) => (
          <div key={field.id} className="flex flex-col relative">
            <div
              onClick={deleteField(index)}
              className="absolute -left-[10px] justify-center items-center flex -top-[10px] w-[20px] h-[20px] bg-red-600 rounded-full text-textColor"
            >
              x
            </div>
            <Subreddit {...register(`subreddit.${index}.value`)} />
          </div>
        ))}
      </div>
      <Button onClick={addField}>Add Channel</Button>
    </>
  );
};

export default withProvider(
  WrapcastProvider,
  undefined,
  undefined,
  async (list) => {
    if (
      list.some((item) => item.some((field) => field.path.indexOf('mp4') > -1))
    ) {
      return 'Warpcast can only accept images';
    }

    return true;
  },
  3000
);
